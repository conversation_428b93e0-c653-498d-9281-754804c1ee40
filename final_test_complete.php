<?php
// 最终完整测试
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🎯 最终完整测试</h2>";

// 数据库配置
$host = '**************';
$dbname = 'likeidai';
$username = 'root123';
$password = 'root';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功<br><br>";
    
    // 测试数据
    $testPhone = '13900166666';
    $testPassword = '123456';
    
    echo "<h3>1. 清理并创建测试用户</h3>";
    
    // 清理旧数据
    $pdo->prepare("DELETE FROM user WHERE phone = ?")->execute([$testPhone]);
    $pdo->prepare("DELETE FROM customer WHERE phone = ?")->execute([$testPhone]);
    
    // 创建测试用户
    $hashedPassword = sha1(md5($testPassword));
    $tuiMa = rand(10000, 99999);
    $addTime = time();
    
    // 插入user表
    $userSql = "INSERT INTO user (
        phone, password, yao_ma, tui_ma, addtime, status, 
        tixianmima, fxmoney, yao_phone, jisuan_ticheng, 
        ticheng_sum, ketixian, shenqing_tixian, leiji_tixian, 
        truename, edu, zhanghuyue, vip, Discount, last_time
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $pdo->prepare($userSql);
    $userResult = $stmt->execute([
        $testPhone, $hashedPassword, '10086', $tuiMa, $addTime, 1,
        rand(100000, 999999), mt_rand(20000, 30000), '', 0, 0, 0, 0, 0, 
        '测试用户', 0, 0, 1, 0, $addTime
    ]);
    
    if ($userResult) {
        echo "✅ user表插入成功<br>";
        
        // 插入customer表
        $customerSql = "INSERT INTO customer (
            customer_name, phone, password, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($customerSql);
        $customerResult = $stmt->execute([
            '测试用户',
            $testPhone,
            $hashedPassword,
            1,
            date('Y-m-d H:i:s'),
            date('Y-m-d H:i:s')
        ]);
        
        if ($customerResult) {
            echo "✅ customer表插入成功<br>";
        }
    }
    
    echo "<h3>2. 验证登录逻辑</h3>";
    
    // 模拟登录验证
    $loginPassword = sha1(md5($testPassword));
    $stmt = $pdo->prepare("SELECT * FROM user WHERE phone = ? AND password = ?");
    $stmt->execute([$testPhone, $loginPassword]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user && $user['status'] == 1) {
        echo "✅ 登录验证成功<br>";
        echo "用户ID: " . $user['id'] . "<br>";
        echo "手机号: " . $user['phone'] . "<br>";
        echo "状态: 正常<br>";
    } else {
        echo "❌ 登录验证失败<br>";
    }
    
    echo "<h3>3. 检查后台数据</h3>";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM customer WHERE phone = ?");
    $stmt->execute([$testPhone]);
    $customerExists = $stmt->fetchColumn();
    
    if ($customerExists) {
        echo "✅ customer表中存在测试用户<br>";
    } else {
        echo "❌ customer表中不存在测试用户<br>";
    }
    
    echo "<h3>4. 检查文件状态</h3>";
    
    $files = [
        'App/Tpl/Home/User_login.html' => '登录页面',
        'App/Tpl/Home/User_signup.html' => '注册页面',
        'Public/home/<USER>/js/login.js' => 'JavaScript文件',
        'App/Lib/Action/Home/UserAction.class.php' => '用户控制器'
    ];
    
    foreach ($files as $file => $name) {
        if (file_exists($file)) {
            $size = filesize($file);
            $modified = date('Y-m-d H:i:s', filemtime($file));
            echo "✅ $name: $size 字节, 修改时间: $modified<br>";
        } else {
            echo "❌ $name: 文件不存在<br>";
        }
    }
    
} catch (PDOException $e) {
    echo "❌ 数据库错误: " . $e->getMessage() . "<br>";
}

echo "<br><div style='background:#e8f5e8;padding:15px;border-radius:5px;'>";
echo "<h3>🎉 测试完成</h3>";
echo "<strong>测试账号信息：</strong><br>";
echo "手机号: $testPhone<br>";
echo "密码: $testPassword<br>";
echo "<br><strong>测试链接：</strong><br>";
echo "1. <a href='http://dailuanshej.cn/index.php/User/login' target='_blank'>登录页面</a><br>";
echo "2. <a href='http://dailuanshej.cn/index.php/User/signup' target='_blank'>注册页面</a><br>";
echo "3. <a href='http://dailuanshej.cn/admin.php/Customer/index' target='_blank'>后台客户管理</a><br>";
echo "</div>";

echo "<br><h3>📋 修复总结</h3>";
echo "1. ✅ 创建了完整的登录页面模板<br>";
echo "2. ✅ 修复了注册功能，移除username字段<br>";
echo "3. ✅ 优化了JavaScript登录逻辑<br>";
echo "4. ✅ 添加了customer表同步功能<br>";
echo "5. ✅ 创建了测试用户数据<br>";

echo "<br><h3>🔧 如果仍然登录失败</h3>";
echo "可能的原因和解决方案：<br>";
echo "1. <strong>缓存问题</strong> - 清除浏览器缓存<br>";
echo "2. <strong>JavaScript错误</strong> - 打开浏览器开发者工具查看控制台错误<br>";
echo "3. <strong>网络问题</strong> - 检查网络连接<br>";
echo "4. <strong>服务器配置</strong> - 检查PHP和数据库配置<br>";

echo "<br><h3>🧪 调试步骤</h3>";
echo "1. 使用测试账号 $testPhone / $testPassword 登录<br>";
echo "2. 如果登录失败，查看浏览器控制台错误信息<br>";
echo "3. 检查网络请求是否正常发送<br>";
echo "4. 确认服务器返回的错误信息<br>";
?>
