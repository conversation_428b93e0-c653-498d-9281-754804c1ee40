<?php
// 调试登录问题
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 调试登录问题</h2>";

// 数据库配置
$host = '**************';
$dbname = 'likeidai';
$username = 'root123';
$password = 'root';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功<br><br>";
    
    echo "<h3>1. 检查现有用户</h3>";
    $stmt = $pdo->prepare("SELECT phone, password, status, addtime FROM user ORDER BY id DESC LIMIT 10");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($users)) {
        echo "<table border='1' style='border-collapse:collapse;'>";
        echo "<tr><th>手机号</th><th>密码(前10位)</th><th>状态</th><th>注册时间</th><th>操作</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['phone'] . "</td>";
            echo "<td>" . substr($user['password'], 0, 10) . "...</td>";
            echo "<td>" . ($user['status'] ? '正常' : '禁用') . "</td>";
            echo "<td>" . date('Y-m-d H:i:s', $user['addtime']) . "</td>";
            echo "<td><a href='?test_phone=" . $user['phone'] . "'>测试登录</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ 没有找到用户数据<br>";
    }
    
    // 如果有测试手机号参数
    if (isset($_GET['test_phone'])) {
        $testPhone = $_GET['test_phone'];
        echo "<br><h3>2. 测试登录: $testPhone</h3>";
        
        // 获取用户信息
        $stmt = $pdo->prepare("SELECT * FROM user WHERE phone = ?");
        $stmt->execute([$testPhone]);
        $userInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($userInfo) {
            echo "✅ 用户存在<br>";
            echo "状态: " . ($userInfo['status'] ? '正常' : '禁用') . "<br>";
            echo "密码: " . $userInfo['password'] . "<br>";
            
            // 测试不同的密码
            $testPasswords = ['123456', '111111', '000000', 'admin', 'password'];
            
            echo "<br><h4>测试常用密码:</h4>";
            foreach ($testPasswords as $testPwd) {
                $hashedPwd = sha1(md5($testPwd));
                $match = ($hashedPwd === $userInfo['password']);
                
                echo "密码 '$testPwd' -> $hashedPwd -> " . ($match ? '✅ 匹配' : '❌ 不匹配') . "<br>";
                
                if ($match) {
                    echo "<div style='background:#e8f5e8;padding:10px;border-radius:5px;margin:10px 0;'>";
                    echo "<strong>🎉 找到正确密码: $testPwd</strong><br>";
                    echo "可以使用此密码登录: <a href='http://dailuanshej.cn/index.php/User/login' target='_blank'>登录页面</a><br>";
                    echo "</div>";
                }
            }
            
            // 如果没有匹配的密码，重置为123456
            $foundMatch = false;
            foreach ($testPasswords as $testPwd) {
                $hashedPwd = sha1(md5($testPwd));
                if ($hashedPwd === $userInfo['password']) {
                    $foundMatch = true;
                    break;
                }
            }
            
            if (!$foundMatch) {
                echo "<br><h4>🔧 重置密码为123456</h4>";
                $newPassword = sha1(md5('123456'));
                $stmt = $pdo->prepare("UPDATE user SET password = ? WHERE phone = ?");
                $result = $stmt->execute([$newPassword, $testPhone]);
                
                if ($result) {
                    echo "✅ 密码重置成功，新密码: 123456<br>";
                    echo "现在可以使用 $testPhone / 123456 登录<br>";
                } else {
                    echo "❌ 密码重置失败<br>";
                }
            }
            
        } else {
            echo "❌ 用户不存在<br>";
        }
    }
    
    echo "<br><h3>3. 创建测试用户</h3>";
    $testPhone = '13900199999';
    $testPassword = '123456';
    
    // 删除可能存在的测试用户
    $pdo->prepare("DELETE FROM user WHERE phone = ?")->execute([$testPhone]);
    $pdo->prepare("DELETE FROM customer WHERE phone = ?")->execute([$testPhone]);
    
    // 创建新的测试用户
    $hashedPassword = sha1(md5($testPassword));
    $tuiMa = rand(10000, 99999);
    $addTime = time();
    
    $userSql = "INSERT INTO user (
        phone, password, yao_ma, tui_ma, addtime, status, 
        tixianmima, fxmoney, yao_phone, jisuan_ticheng, 
        ticheng_sum, ketixian, shenqing_tixian, leiji_tixian, 
        truename, edu, zhanghuyue, vip, Discount, last_time
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $pdo->prepare($userSql);
    $result = $stmt->execute([
        $testPhone, $hashedPassword, '10086', $tuiMa, $addTime, 1,
        rand(100000, 999999), mt_rand(20000, 30000), '', 0, 0, 0, 0, 0, 
        '', 0, 0, 1, 0, $addTime
    ]);
    
    if ($result) {
        echo "✅ 测试用户创建成功<br>";
        echo "手机号: $testPhone<br>";
        echo "密码: $testPassword<br>";
        echo "加密后: $hashedPassword<br>";
        
        // 同时创建customer记录
        $customerSql = "INSERT INTO customer (
            customer_name, phone, password, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($customerSql);
        $stmt->execute([
            '测试用户',
            $testPhone,
            $hashedPassword,
            1,
            date('Y-m-d H:i:s'),
            date('Y-m-d H:i:s')
        ]);
        
        echo "✅ customer表记录创建成功<br>";
        
        echo "<br><div style='background:#e8f5e8;padding:15px;border-radius:5px;'>";
        echo "<h4>🧪 测试登录</h4>";
        echo "1. 访问登录页面: <a href='http://dailuanshej.cn/index.php/User/login' target='_blank'>登录页面</a><br>";
        echo "2. 使用账号: $testPhone<br>";
        echo "3. 使用密码: $testPassword<br>";
        echo "4. 检查后台: <a href='http://dailuanshej.cn/admin.php/Customer/index' target='_blank'>客户管理</a><br>";
        echo "</div>";
        
    } else {
        echo "❌ 测试用户创建失败<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ 数据库错误: " . $e->getMessage() . "<br>";
}

echo "<br><h3>📋 调试总结</h3>";
echo "1. 检查了现有用户的密码和状态<br>";
echo "2. 测试了常用密码的匹配情况<br>";
echo "3. 创建了新的测试用户<br>";
echo "4. 如果登录仍然失败，可能是前端JavaScript或后端逻辑问题<br>";
?>
