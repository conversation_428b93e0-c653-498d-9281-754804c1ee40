<?php
// 测试登录修复
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 测试登录修复</h2>";

// 数据库配置
$host = '**************';
$dbname = 'likeidai';
$username = 'root123';
$password = 'root';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ 数据库连接成功<br><br>";
    
    // 测试数据
    $testPhone = '13900188888';
    $testPassword = '123456';
    $testYaoMa = '10086';
    
    echo "<h3>1. 清理测试数据</h3>";
    $pdo->prepare("DELETE FROM user WHERE phone = ?")->execute([$testPhone]);
    $pdo->prepare("DELETE FROM customer WHERE phone = ?")->execute([$testPhone]);
    echo "✅ 清理完成<br>";
    
    echo "<h3>2. 模拟注册流程</h3>";
    $hashedPassword = sha1(md5($testPassword));
    $tuiMa = rand(10000, 99999);
    $addTime = time();
    
    // 插入user表
    $userSql = "INSERT INTO user (
        phone, password, yao_ma, tui_ma, addtime, status, 
        tixianmima, fxmoney, yao_phone, jisuan_ticheng, 
        ticheng_sum, ketixian, shenqing_tixian, leiji_tixian, 
        truename, edu, zhanghuyue, vip, Discount, last_time
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $pdo->prepare($userSql);
    $userResult = $stmt->execute([
        $testPhone, $hashedPassword, $testYaoMa, $tuiMa, $addTime, 1,
        rand(100000, 999999), mt_rand(20000, 30000), '', 0, 0, 0, 0, 0, 
        '', 0, 0, 1, 0, $addTime
    ]);
    
    if ($userResult) {
        echo "✅ user表插入成功<br>";
        
        // 插入customer表
        $customerSql = "INSERT INTO customer (
            customer_name, phone, password, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($customerSql);
        $customerResult = $stmt->execute([
            '用户' . substr($testPhone, -4),
            $testPhone,
            $hashedPassword,
            1,
            date('Y-m-d H:i:s'),
            date('Y-m-d H:i:s')
        ]);
        
        if ($customerResult) {
            echo "✅ customer表插入成功<br>";
        }
    }
    
    echo "<h3>3. 测试登录验证</h3>";
    
    // 模拟登录验证
    $loginPassword = sha1(md5($testPassword));
    $stmt = $pdo->prepare("SELECT * FROM user WHERE phone = ? AND password = ?");
    $stmt->execute([$testPhone, $loginPassword]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "✅ 登录验证成功<br>";
        echo "用户信息：<br>";
        echo "- ID: " . $user['id'] . "<br>";
        echo "- 手机号: " . $user['phone'] . "<br>";
        echo "- 状态: " . ($user['status'] ? '正常' : '禁用') . "<br>";
        echo "- 注册时间: " . date('Y-m-d H:i:s', $user['addtime']) . "<br>";
        
        if ($user['status'] == 1) {
            echo "✅ 账户状态正常，可以登录<br>";
        } else {
            echo "❌ 账户被禁用<br>";
        }
    } else {
        echo "❌ 登录验证失败<br>";
        
        // 调试信息
        echo "<h4>🔍 调试信息</h4>";
        echo "查询手机号: $testPhone<br>";
        echo "原始密码: $testPassword<br>";
        echo "加密密码: $loginPassword<br>";
        
        // 检查用户是否存在
        $stmt = $pdo->prepare("SELECT phone, password, status FROM user WHERE phone = ?");
        $stmt->execute([$testPhone]);
        $userCheck = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($userCheck) {
            echo "用户存在，密码对比：<br>";
            echo "- 数据库密码: " . $userCheck['password'] . "<br>";
            echo "- 登录密码: " . $loginPassword . "<br>";
            echo "- 密码匹配: " . ($userCheck['password'] === $loginPassword ? '是' : '否') . "<br>";
            echo "- 账户状态: " . $userCheck['status'] . "<br>";
        } else {
            echo "用户不存在<br>";
        }
    }
    
    echo "<h3>4. 测试前端登录</h3>";
    echo "测试登录页面: <a href='http://dailuanshej.cn/index.php/User/login' target='_blank'>登录页面</a><br>";
    echo "测试账号: $testPhone<br>";
    echo "测试密码: $testPassword<br>";
    
    echo "<h3>5. 检查后台客户管理</h3>";
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM customer WHERE phone = ?");
    $stmt->execute([$testPhone]);
    $customerExists = $stmt->fetchColumn();
    
    if ($customerExists) {
        echo "✅ customer表中存在测试用户<br>";
        echo "后台客户管理: <a href='http://dailuanshej.cn/admin.php/Customer/index' target='_blank'>客户管理</a><br>";
    } else {
        echo "❌ customer表中不存在测试用户<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ 数据库错误: " . $e->getMessage() . "<br>";
}

echo "<br><div style='background:#e8f5e8;padding:15px;border-radius:5px;'>";
echo "<h3>🎯 测试结论</h3>";
echo "1. 如果登录验证成功，说明密码加密逻辑正确<br>";
echo "2. 如果customer表有数据，说明后台可以看到用户<br>";
echo "3. 可以使用测试账号在前端登录验证<br>";
echo "</div>";

echo "<br><h3>📋 下一步操作</h3>";
echo "1. 使用测试账号登录: <a href='http://dailuanshej.cn/index.php/User/login' target='_blank'>登录测试</a><br>";
echo "2. 注册新用户测试: <a href='http://dailuanshej.cn/index.php/User/signup' target='_blank'>注册测试</a><br>";
echo "3. 检查后台管理: <a href='http://dailuanshej.cn/admin.php/Customer/index' target='_blank'>后台管理</a><br>";
?>
