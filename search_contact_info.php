<?php
// 搜索包含联系方式的文件
echo "<h2>🔍 搜索包含联系方式的文件</h2>";

$searchTerms = [
    '18108197',
    'Kong_Money',
    '客户使用协议',
    'zzmaku.com',
    '站长源码库'
];

function searchInDirectory($dir, $searchTerms) {
    $results = [];
    
    if (!is_dir($dir)) {
        return $results;
    }
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir),
        RecursiveIteratorIterator::SELF_FIRST
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && (
            $file->getExtension() === 'php' || 
            $file->getExtension() === 'html' || 
            $file->getExtension() === 'js' || 
            $file->getExtension() === 'css'
        )) {
            $content = file_get_contents($file->getPathname());
            
            foreach ($searchTerms as $term) {
                if (stripos($content, $term) !== false) {
                    $results[] = [
                        'file' => $file->getPathname(),
                        'term' => $term,
                        'size' => $file->getSize()
                    ];
                }
            }
        }
    }
    
    return $results;
}

echo "<h3>搜索关键词：</h3>";
foreach ($searchTerms as $term) {
    echo "- " . htmlspecialchars($term) . "<br>";
}

echo "<br><h3>搜索结果：</h3>";

$directories = [
    'App',
    'Public'
];

$allResults = [];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        echo "<h4>搜索目录: $dir</h4>";
        $results = searchInDirectory($dir, $searchTerms);
        
        if (empty($results)) {
            echo "✅ 该目录中未找到包含联系方式的文件<br>";
        } else {
            foreach ($results as $result) {
                echo "❌ 发现文件: " . $result['file'] . " (包含: " . $result['term'] . ")<br>";
                $allResults[] = $result;
            }
        }
        echo "<br>";
    }
}

if (!empty($allResults)) {
    echo "<h3>🔧 需要清理的文件：</h3>";
    
    $uniqueFiles = array_unique(array_column($allResults, 'file'));
    
    foreach ($uniqueFiles as $file) {
        echo "<h4>📄 " . basename($file) . "</h4>";
        echo "路径: " . $file . "<br>";
        
        if (file_exists($file)) {
            $content = file_get_contents($file);
            
            echo "发现的关键词：<br>";
            foreach ($searchTerms as $term) {
                if (stripos($content, $term) !== false) {
                    echo "- " . htmlspecialchars($term) . "<br>";
                    
                    // 显示包含关键词的行
                    $lines = explode("\n", $content);
                    foreach ($lines as $lineNum => $line) {
                        if (stripos($line, $term) !== false) {
                            echo "  第" . ($lineNum + 1) . "行: " . htmlspecialchars(trim($line)) . "<br>";
                        }
                    }
                }
            }
            
            echo "<br>";
        }
    }
    
    echo "<h3>🧹 自动清理</h3>";
    echo "正在清理包含联系方式的内容...<br>";
    
    foreach ($uniqueFiles as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $originalContent = $content;
            
            // 清理联系方式
            $content = str_ireplace('18108197', '', $content);
            $content = str_ireplace('Kong_Money', '', $content);
            $content = str_ireplace('zzmaku.com', '', $content);
            $content = str_ireplace('站长源码库', '', $content);
            
            // 清理包含联系方式的整行
            $lines = explode("\n", $content);
            $cleanLines = [];
            
            foreach ($lines as $line) {
                $shouldRemove = false;
                foreach ($searchTerms as $term) {
                    if (stripos($line, $term) !== false) {
                        $shouldRemove = true;
                        break;
                    }
                }
                
                if (!$shouldRemove) {
                    $cleanLines[] = $line;
                }
            }
            
            $content = implode("\n", $cleanLines);
            
            if ($content !== $originalContent) {
                if (file_put_contents($file, $content)) {
                    echo "✅ 清理完成: " . basename($file) . "<br>";
                } else {
                    echo "❌ 清理失败: " . basename($file) . "<br>";
                }
            } else {
                echo "ℹ️ 无需清理: " . basename($file) . "<br>";
            }
        }
    }
    
} else {
    echo "<div style='background:#e8f5e8;padding:15px;border-radius:5px;'>";
    echo "<h3>✅ 清理完成</h3>";
    echo "未找到包含联系方式的文件，或已经清理完成。<br>";
    echo "</div>";
}

echo "<br><h3>📋 清理总结</h3>";
echo "已搜索的目录：<br>";
foreach ($directories as $dir) {
    echo "- $dir<br>";
}

echo "<br>搜索的文件类型：<br>";
echo "- PHP文件 (.php)<br>";
echo "- HTML文件 (.html)<br>";
echo "- JavaScript文件 (.js)<br>";
echo "- CSS文件 (.css)<br>";
?>
