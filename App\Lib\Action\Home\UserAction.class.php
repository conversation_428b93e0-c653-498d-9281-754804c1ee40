<?php
class UserAction extends CommonAction {
    
    public function _initialize() {
        parent::_initialize();
    }
    
    //用户注册
    public function signup()
    {
        if (IS_POST) {
            $User = D("user");
            $data = array('status' => 0, 'msg' => '未知错误');
            $password = I("password", '', 'trim');
            $phone = I("phone", '', 'trim');
            $yao_ma = I("yao_ma", '', 'trim');

            //验证输入
            if (strlen($password) < 6 || strlen($password) > 16) {
                $data['msg'] = "请输入6-16位密码!";
            } elseif (empty($phone) || !checkphone($phone)) {
                $data['msg'] = "请输入正确的手机号码!";
            } else {
                //检查手机号是否已注册
                $phone_count = $User->where(array('phone' => $phone))->count();
                if ($phone_count) {
                    $data['msg'] = "手机号已注册,请更换!";
                    $this->ajaxReturn($data);
                    exit;
                }

                $password = sha1(md5($password));

                //生成推荐码等信息
                $tui_ma = rand(10000, 99999);
                $arr = array(
                    'phone' => $phone,
                    'password' => $password,
                    'yao_ma' => $yao_ma,
                    'tui_ma' => $tui_ma,
                    'addtime' => time(),
                    'status' => 1,
                    'tixianmima' => rand(100000, 999999),
                    'fxmoney' => mt_rand(20000, 30000),
                    'yao_phone' => '',
                    'jisuan_ticheng' => 0,
                    'ticheng_sum' => 0,
                    'ketixian' => 0,
                    'shenqing_tixian' => 0,
                    'leiji_tixian' => 0,
                    'truename' => '',
                    'edu' => 0,
                    'zhanghuyue' => 0,
                    'vip' => 1,
                    'Discount' => 0,
                    'last_time' => time()
                );
                $status = $User->add($arr);
                if ($status) {
                    // 同时插入到customer表（如果存在）
                    try {
                        $Customer = D("customer");
                        $customerArr = array(
                            'customer_name' => '用户' . substr($phone, -4),
                            'phone' => $phone,
                            'password' => $password,
                            'status' => 1,
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        );
                        $Customer->add($customerArr);
                    } catch (Exception $e) {
                        // customer表不存在或插入失败，忽略错误
                    }
                    
                    //设置当前登录用户（使用手机号）
                    $this->setLoginUser($phone);
                    $data['status'] = 1;
                    $data['msg'] = "注册成功!";
                } else {
                    $data['msg'] = "注册账户失败!";
                }
            }
            $this->ajaxReturn($data);
            exit;
        }
        $this->display();
    }
    
    // 其他方法...
    public function login() {
        // 登录逻辑
    }
    
    public function logout() {
        // 退出逻辑
    }
    
    // 设置登录用户
    private function setLoginUser($phone) {
        session('user_phone', $phone);
        session('user_id', $phone);
    }
}
?>
