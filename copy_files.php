<?php
// 复制修复的文件到源码目录
echo "<h2>📁 复制修复文件到源码目录</h2>";

$files = [
    [
        'source' => '../App/Tpl/Home/User_signup.html',
        'target' => 'App/Tpl/Home/User_signup.html'
    ],
    [
        'source' => '../Public/home/<USER>/js/login.js',
        'target' => 'Public/home/<USER>/js/login.js'
    ],
    [
        'source' => '../App/Lib/Action/Home/UserAction.class.php',
        'target' => 'App/Lib/Action/Home/UserAction.class.php'
    ]
];

foreach ($files as $file) {
    echo "<h3>📄 " . basename($file['target']) . "</h3>";
    
    if (file_exists($file['source'])) {
        // 确保目标目录存在
        $targetDir = dirname($file['target']);
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0755, true);
            echo "✅ 创建目录: $targetDir<br>";
        }
        
        // 复制文件
        if (copy($file['source'], $file['target'])) {
            echo "✅ 复制成功: " . $file['source'] . " → " . $file['target'] . "<br>";
            
            // 检查文件大小
            $sourceSize = filesize($file['source']);
            $targetSize = filesize($file['target']);
            echo "📊 源文件大小: $sourceSize 字节<br>";
            echo "📊 目标文件大小: $targetSize 字节<br>";
            
            if ($sourceSize === $targetSize) {
                echo "✅ 文件大小匹配<br>";
            } else {
                echo "❌ 文件大小不匹配<br>";
            }
            
        } else {
            echo "❌ 复制失败: " . $file['source'] . " → " . $file['target'] . "<br>";
        }
    } else {
        echo "❌ 源文件不存在: " . $file['source'] . "<br>";
    }
    
    echo "<br>";
}

echo "<h3>🔍 验证源码目录文件</h3>";

foreach ($files as $file) {
    $target = $file['target'];
    if (file_exists($target)) {
        $size = filesize($target);
        $modified = date('Y-m-d H:i:s', filemtime($target));
        echo "✅ $target: $size 字节, 修改时间: $modified<br>";
        
        // 检查修复标记
        $content = file_get_contents($target);
        if (strpos($content, 'FIXED') !== false) {
            echo "✅ 包含修复标记<br>";
        } else {
            echo "⚠️ 未包含修复标记<br>";
        }
        
    } else {
        echo "❌ $target 不存在<br>";
    }
}

echo "<br><div style='background:#e8f5e8;padding:15px;border-radius:5px;'>";
echo "<h3>🎉 文件更新完成</h3>";
echo "所有修复的文件已复制到源码目录中，现在可以直接上传这些文件到服务器！<br>";
echo "</div>";
?>
