<?php
// 检查文件大小和修改状态
echo "<h2>📁 文件大小和修改状态检查</h2>";

$files = [
    'App/Tpl/Home/User_signup.html',
    'Public/home/<USER>/js/login.js',
    'App/Lib/Action/Home/UserAction.class.php'
];

foreach ($files as $file) {
    echo "<h3>📄 " . basename($file) . "</h3>";
    
    if (file_exists($file)) {
        $size = filesize($file);
        $modified = date('Y-m-d H:i:s', filemtime($file));
        
        echo "✅ 文件存在<br>";
        echo "📊 文件大小: " . $size . " 字节<br>";
        echo "🕒 修改时间: " . $modified . "<br>";
        
        // 检查文件内容特征
        $content = file_get_contents($file);
        
        if (strpos($file, 'User_signup.html') !== false) {
            if (strpos($content, 'FIXED VERSION') !== false) {
                echo "✅ 包含修复标记<br>";
            } else {
                echo "❌ 未包含修复标记<br>";
            }
            
            if (strpos($content, 'name="username"') !== false) {
                echo "❌ 仍包含username字段<br>";
            } else {
                echo "✅ 已移除username字段<br>";
            }
        }
        
        if (strpos($file, 'login.js') !== false) {
            if (strpos($content, 'FIXED VERSION') !== false) {
                echo "✅ 包含修复标记<br>";
            } else {
                echo "❌ 未包含修复标记<br>";
            }
            
            if (strpos($content, 'username:username') !== false) {
                echo "❌ 仍包含username提交<br>";
            } else {
                echo "✅ 已移除username提交<br>";
            }
        }
        
        if (strpos($file, 'UserAction.class.php') !== false) {
            if (strpos($content, 'customer') !== false) {
                echo "✅ 包含customer表同步<br>";
            } else {
                echo "❌ 未包含customer表同步<br>";
            }
        }
        
    } else {
        echo "❌ 文件不存在<br>";
    }
    
    echo "<br>";
}

echo "<h3>🔧 强制重新创建文件</h3>";

// 强制重新创建User_signup.html
$signupContent = '<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>注册 - 小贷系统 [FIXED VERSION ' . date('Y-m-d H:i:s') . ']</title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/login--1.css">
</head>

<body>
	<div class="head-bg register">
		<div class="head">
			<div class="head_top">
				<div class="head_logo">
					<img src="__PUBLIC__/home/<USER>/picture/logo.png" alt="">
				</div>
				<div class="head_title">
					<Somnus:sitecfg name="sitetitle" />
				</div>
			</div>
			<div class="head_bottom">
				<div class="head_login" onclick="javascript:window.location.href=\'{:U(\'User/login\')}\'">
					登录
				</div>
				<div class="head_register">
					注册
				</div>
			</div>
		</div>
		<div class="white_q"></div>
	</div>

	<div class="login-box">
		<div class="">
			<div class="form-box am-u-sm-11 am-u-sm-centered">
				<form class="am-form am-form-horizontal" id="form-with-tooltip">
					<!-- 手机号输入 -->
					<div class="am-form-group">
						<div class="am-g input-box">
							<label class="am-u-sm-1" style="padding: 0;">
								<img class="menu-icon" src="__PUBLIC__/home/<USER>/picture/phone.png" alt="">
							</label>
							<div class="am-u-sm-11 f_number" style="padding: 0;">
								<input type="number" name="phone" id="phone" minlength="11" maxlength="11" placeholder="输入你的手机号" required/>
							</div>
						</div>
					</div>
					
					<!-- 密码输入 -->
					<div class="am-form-group">
						<div class="am-g input-box">
							<label class="am-u-sm-1" style="padding: 0;">
								<img class="menu-icon" src="__PUBLIC__/home/<USER>/picture/pwd.png" alt="">
							</label>
							<div class="am-u-sm-11 f_number" style="padding: 0;">
								<input type="password" name="password" id="password" minlength="6" maxlength="15" placeholder="设置你的密码" required/>
							</div>
						</div>
					</div>
					
					<!-- 隐藏的推荐码 -->
					<input type="hidden" name="referral" id="referral" value="10086"/>
					
					<!-- 服务协议 -->
					<div class="am-form-group" style="text-align:center; font-size: 12px; color: #777;">
						注册即代表您已阅读并同意<br>
						<u class="zcfwxy">《注册服务协议》</u>
					</div>

					<div style="height: 15px;"></div>

					<!-- 注册按钮 -->
					<div class="am-form-group">
						<div class="">
							<button type="button" id="register-button" class="am-btn">提交注册</button>
						</div>
						<div style="height: 10px;"></div>
						<div style="text-align: center;">
							<span style="color: rgb(255, 209, 27);" onclick="javascript:window.location.href=\'{:U(\'User/login\')}\'">已有账户，返回登录</span>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>

	<div class="message">
		<p></p>
	</div>

	<script type="text/javascript">
		document.documentElement.addEventListener(\'touchmove\', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
		var sms_off = {:C(\'cfg_sms_off\')};
	</script>

	<!--[if lt IE 9]>
<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
<script src="__PUBLIC__/home/<USER>/js/modernizr.js"></script>
<script src="__PUBLIC__/home/<USER>/js/amazeui.ie8polyfill.min.js"></script>
<![endif]-->

	<!--[if (gte IE 9)|!(IE)]><!-->
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
	<script type="text/javascript" src="__PUBLIC__/home/<USER>/js/login.js"></script>
	<script>
		$(function () {
			var $zcfwxy = $(\'#my-popup-zcfwxy\');
			$(".zcfwxy").unbind(\'click\').on(\'click\', function () {
				$zcfwxy.modal(\'toggle\');
			});
		});
	</script>

	<div style="display: none;">
		<Somnus:sitecfg name="sitecode" />
	</div>

</body>
</html>';

if (file_put_contents('App/Tpl/Home/User_signup.html', $signupContent)) {
    echo "✅ User_signup.html 重新创建成功<br>";
} else {
    echo "❌ User_signup.html 重新创建失败<br>";
}

// 强制重新创建login.js
$loginJsContent = '// FIXED VERSION - 修复注册功能 ' . date('Y-m-d H:i:s') . '
var msg = \'\';

function mesg_default() {
    msg = \'\';
    $(".message p").html(\'\');
    $(".message").hide();
}

function message(msg) {
    $(".message p").html(msg);
    $(".message").show();
    setTimeout(function() {
        $(".message").hide();
    }, 3000);
}

$(function() {
    // 登录操作
    $("#login-button").unbind("click").on("click", function() {
        var phone = $("#phone").val();
        var password = $("#password").val();
        var verifycode = $("#verifycode").val();

        mesg_default();

        if (phone == \'\' || password == \'\') {
            msg = \'请完整填写信息\';
            message(msg);
            return;
        } else {
            if (password.length > 15 || password.length < 6) {
                if (msg == \'\') {
                    msg = \'密码长度应为6-15位\';
                } else {
                    msg += \'</br>密码长度应为6-15位\';
                }
            }
            if (phone.length != 11) {
                if (msg == \'\') {
                    msg = \'手机号码长度应为11位\';
                } else {
                    msg += \'</br>手机号码长度应为11位\';
                }
            }

            if (msg != \'\') {
                message(msg);
                return;
            }
        }

        $.post(
            "/index.php/User/login",
            {
                phone: phone,
                password: password,
                verifycode: verifycode
            },
            function(data, state) {
                if (state != "success") {
                    message("请求失败,请重试");
                    return false;
                } else {
                    if (data.status == 1) {
                        message("登录成功,正在跳转...");
                        setTimeout(function() {
                            window.location.href = "/index.php/Index/index";
                        }, 1000);
                    } else {
                        message(data.msg);
                    }
                }
            },
            \'json\'
        );
    });

    // 注册操作 - 修复版本，移除username字段
    $("#register-button").unbind("click").on("click", function() {
        var password = $("#password").val();
        var phone = $("#phone").val();
        var referral = $("#referral").val();

        mesg_default();

        if (password == \'\' || phone == \'\' || referral == \'\') {
            msg = \'请完整填写信息\';
            message(msg);
            return;
        } else {
            if (password.length > 15 || password.length < 6) {
                if (msg == \'\') {
                    msg = \'密码长度应为6-15位\';
                } else {
                    msg += \'</br>密码长度应为6-15位\';
                }
            }
            if (phone.length != 11) {
                if (msg == \'\') {
                    msg = \'手机号码长度应为11位\';
                } else {
                    msg += \'</br>手机号码长度应为11位\';
                }
            }

            if (msg != \'\') {
                message(msg);
                return;
            }
        }

        $.post(
            "/index.php/User/signup",
            {
                phone: phone,
                password: password,
                yao_ma: referral
            },
            function(data, state) {
                if (state != "success") {
                    message("请求失败,请重试");
                    return false;
                } else {
                    if (data.status == 1) {
                        message("注册成功,正在跳转...");
                        setTimeout(function() {
                            window.location.href = "/index.php/Index/index";
                        }, 1000);
                    } else {
                        message(data.msg);
                    }
                }
            },
            \'json\'
        );
    });
});';

if (file_put_contents('Public/home/<USER>/js/login.js', $loginJsContent)) {
    echo "✅ login.js 重新创建成功<br>";
} else {
    echo "❌ login.js 重新创建失败<br>";
}

echo "<br><h3>📊 重新检查文件大小</h3>";
foreach ($files as $file) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo basename($file) . ": " . $size . " 字节<br>";
    }
}
?>
