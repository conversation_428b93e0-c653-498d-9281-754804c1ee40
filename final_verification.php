<?php
// 最终验证源码文件修复状态
echo "<h2>🔍 最终验证源码文件修复状态</h2>";

$files = [
    'App/Tpl/Home/User_signup.html' => [
        'name' => '注册页面',
        'checks' => [
            'FIXED VERSION' => '包含修复标记',
            'name="username"' => '不应包含username字段',
            'name="phone"' => '应包含phone字段',
            'name="password"' => '应包含password字段',
            'value="10086"' => '应包含默认推荐码'
        ]
    ],
    'Public/home/<USER>/js/login.js' => [
        'name' => 'JavaScript文件',
        'checks' => [
            'FIXED VERSION' => '包含修复标记',
            'username:username' => '不应包含username提交',
            'phone:phone' => '应包含phone提交',
            'password:password' => '应包含password提交',
            'yao_ma:referral' => '应包含yao_ma提交'
        ]
    ],
    'App/Lib/Action/Home/UserAction.class.php' => [
        'name' => '用户控制器',
        'checks' => [
            '同时插入到customer表' => '包含customer表同步',
            'D("customer")' => '使用customer模型',
            'customer_name' => '设置客户姓名',
            'created_at' => '设置创建时间'
        ]
    ]
];

$allPassed = true;

foreach ($files as $filePath => $fileInfo) {
    echo "<h3>📄 {$fileInfo['name']} ({$filePath})</h3>";
    
    if (file_exists($filePath)) {
        $size = filesize($filePath);
        $modified = date('Y-m-d H:i:s', filemtime($filePath));
        $content = file_get_contents($filePath);
        
        echo "✅ 文件存在<br>";
        echo "📊 文件大小: $size 字节<br>";
        echo "🕒 修改时间: $modified<br>";
        
        echo "<h4>🔍 内容检查:</h4>";
        foreach ($fileInfo['checks'] as $searchText => $description) {
            $found = strpos($content, $searchText) !== false;
            
            if (strpos($description, '不应包含') !== false) {
                // 反向检查 - 不应该包含的内容
                if (!$found) {
                    echo "✅ $description: 通过<br>";
                } else {
                    echo "❌ $description: 失败 (仍然包含 '$searchText')<br>";
                    $allPassed = false;
                }
            } else {
                // 正向检查 - 应该包含的内容
                if ($found) {
                    echo "✅ $description: 通过<br>";
                } else {
                    echo "❌ $description: 失败 (未找到 '$searchText')<br>";
                    $allPassed = false;
                }
            }
        }
        
    } else {
        echo "❌ 文件不存在<br>";
        $allPassed = false;
    }
    
    echo "<br>";
}

echo "<div style='background:" . ($allPassed ? '#e8f5e8' : '#ffe8e8') . ";padding:15px;border-radius:5px;'>";
if ($allPassed) {
    echo "<h3>🎉 所有检查通过！</h3>";
    echo "✅ 所有文件已正确修复<br>";
    echo "✅ 注册页面移除了username字段<br>";
    echo "✅ JavaScript移除了username提交<br>";
    echo "✅ 后端控制器添加了customer表同步<br>";
    echo "<br><strong>现在可以直接上传这些文件到服务器了！</strong><br>";
} else {
    echo "<h3>⚠️ 部分检查未通过</h3>";
    echo "请检查上述失败的项目并重新修复。<br>";
}
echo "</div>";

echo "<br><h3>📋 部署清单</h3>";
echo "需要上传到服务器的文件：<br>";
echo "1. <code>App/Tpl/Home/User_signup.html</code><br>";
echo "2. <code>Public/home/<USER>/js/login.js</code><br>";
echo "3. <code>App/Lib/Action/Home/UserAction.class.php</code><br>";

echo "<br><h3>🧪 测试步骤</h3>";
echo "1. 上传文件到服务器<br>";
echo "2. 访问注册页面: <a href='http://dailuanshej.cn/index.php/User/signup' target='_blank'>测试注册</a><br>";
echo "3. 注册一个测试用户<br>";
echo "4. 检查后台客户管理: <a href='http://dailuanshej.cn/admin.php/Customer/index' target='_blank'>后台管理</a><br>";
echo "5. 使用注册的账号登录测试<br>";

echo "<br><h3>🔧 修复内容总结</h3>";
echo "1. <strong>移除username字段</strong> - 注册只需要手机号和密码<br>";
echo "2. <strong>修复数据库字段映射</strong> - 避免插入不存在的字段<br>";
echo "3. <strong>添加customer表同步</strong> - 注册时同时插入user和customer表<br>";
echo "4. <strong>优化前端验证</strong> - 简化JavaScript验证逻辑<br>";
echo "5. <strong>解决日期字段问题</strong> - 避免MySQL严格模式错误<br>";
?>
