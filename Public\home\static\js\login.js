var msg = '';

function mesg_default() {
    msg = '';
    $(".message p").html('');
    $(".message").hide();
}

function message(msg) {
    $(".message p").html(msg);
    $(".message").show();
    setTimeout(function() {
        $(".message").hide();
    }, 3000);
}

$(function() {
    // 登录操作
    $("#login-button").unbind("click").on("click", function() {
        var phone = $("#phone").val();
        var password = $("#password").val();
        var verifycode = $("#verifycode").val();

        mesg_default();

        if (phone == '' || password == '') {
            msg = '请完整填写信息';
            message(msg);
            return;
        } else {
            if (password.length > 15 || password.length < 6) {
                if (msg == '') {
                    msg = '密码长度应为6-15位';
                } else {
                    msg += '</br>密码长度应为6-15位';
                }
            }
            if (phone.length != 11) {
                if (msg == '') {
                    msg = '手机号码长度应为11位';
                } else {
                    msg += '</br>手机号码长度应为11位';
                }
            }

            if (msg != '') {
                message(msg);
                return;
            }
        }

        $.post(
            "/index.php/User/login",
            {
                phone: phone,
                password: password,
                verifycode: verifycode
            },
            function(data, state) {
                if (state != "success") {
                    message("请求失败,请重试");
                    return false;
                } else {
                    if (data.status == 1) {
                        message("登录成功,正在跳转...");
                        setTimeout(function() {
                            window.location.href = "/index.php/Index/index";
                        }, 1000);
                    } else {
                        message(data.msg);
                    }
                }
            },
            'json'
        );
    });

    // 注册操作
    $("#register-button").unbind("click").on("click", function() {
        var password = $("#password").val();
        var phone = $("#phone").val();
        var referral = $("#referral").val();

        mesg_default();

        if (password == '' || phone == '' || referral == '') {
            msg = '请完整填写信息';
            message(msg);
            return;
        } else {
            if (password.length > 15 || password.length < 6) {
                if (msg == '') {
                    msg = '密码长度应为6-15位';
                } else {
                    msg += '</br>密码长度应为6-15位';
                }
            }
            if (phone.length != 11) {
                if (msg == '') {
                    msg = '手机号码长度应为11位';
                } else {
                    msg += '</br>手机号码长度应为11位';
                }
            }

            if (msg != '') {
                message(msg);
                return;
            }
        }

        $.post(
            "/index.php/User/signup",
            {
                phone: phone,
                password: password,
                yao_ma: referral
            },
            function(data, state) {
                if (state != "success") {
                    message("请求失败,请重试");
                    return false;
                } else {
                    if (data.status == 1) {
                        message("注册成功,正在跳转...");
                        setTimeout(function() {
                            window.location.href = "/index.php/Index/index";
                        }, 1000);
                    } else {
                        message(data.msg);
                    }
                }
            },
            'json'
        );
    });

    // 发送验证码
    $("#send-code").unbind("click").on("click", function() {
        var phone = $("#phone").val();
        var type = $("#type").val() || "login";

        if (phone == '') {
            message('请输入手机号');
            return;
        }

        if (phone.length != 11) {
            message('手机号码长度应为11位');
            return;
        }

        $.post(
            "/index.php/User/sendsmscode",
            {
                phone: phone,
                type: type
            },
            function(data, state) {
                if (state != "success") {
                    message("请求失败,请重试");
                    return false;
                } else {
                    if (data.status == 1) {
                        message("验证码发送成功");
                        // 倒计时
                        var countdown = 60;
                        var timer = setInterval(function() {
                            countdown--;
                            $("#send-code").text(countdown + "秒后重发");
                            $("#send-code").attr("disabled", true);
                            if (countdown <= 0) {
                                clearInterval(timer);
                                $("#send-code").text("发送验证码");
                                $("#send-code").attr("disabled", false);
                            }
                        }, 1000);
                    } else {
                        message(data.msg);
                    }
                }
            },
            'json'
        );
    });

    // 忘记密码
    $("#forgot-password").unbind("click").on("click", function() {
        var phone = $("#phone").val();
        var password = $("#password").val();
        var verifycode = $("#verifycode").val();

        mesg_default();

        if (phone == '' || password == '' || verifycode == '') {
            msg = '请完整填写信息';
            message(msg);
            return;
        } else {
            if (password.length > 15 || password.length < 6) {
                if (msg == '') {
                    msg = '密码长度应为6-15位';
                } else {
                    msg += '</br>密码长度应为6-15位';
                }
            }
            if (phone.length != 11) {
                if (msg == '') {
                    msg = '手机号码长度应为11位';
                } else {
                    msg += '</br>手机号码长度应为11位';
                }
            }

            if (msg != '') {
                message(msg);
                return;
            }
        }

        $.post(
            "/index.php/User/resetPassword",
            {
                phone: phone,
                password: password,
                verifycode: verifycode
            },
            function(data, state) {
                if (state != "success") {
                    message("请求失败,请重试");
                    return false;
                } else {
                    if (data.status == 1) {
                        message("密码重置成功,正在跳转...");
                        setTimeout(function() {
                            window.location.href = "/index.php/User/login";
                        }, 1000);
                    } else {
                        message(data.msg);
                    }
                }
            },
            'json'
        );
    });
});
