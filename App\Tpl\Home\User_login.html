<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="description" content="">
	<meta name="keywords" content="">
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/amazeui.min.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/app.css">
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/all.css">
	<title>登录 - 小贷系统 [FIXED VERSION]</title>
	<link rel="stylesheet" href="__PUBLIC__/home/<USER>/css/login--1.css">
</head>

<body>
	<div class="head-bg login">
		<div class="websitename_box">
			<div class="websitename">
				<div>
					<Somnus:sitecfg name="sitetitle" />
				</div>
			</div>
		</div>
		<div class="head_bottom_box">
			<div class="head_bottom">
				<div class="head_login">
					登录
				</div>
				<div class="head_register" onclick="javascript:window.location.href='{:U('User/signup')}'">
					注册
				</div>
			</div>
		</div>
		<div class="white_q"></div>
	</div>

	<div class="login">
		<div class="">
			<div class="form-box am-u-sm-11 am-u-sm-centered">
				<form class="am-form am-form-horizontal" id="form-with-tooltip">
					<!-- 手机号输入 -->
					<div class="am-form-group">
						<div class="am-g input-box">
							<label class="am-u-sm-1" style="padding: 0;">
								<img class="menu-icon" src="__PUBLIC__/home/<USER>/picture/phone.png" alt="">
							</label>
							<div class="am-u-sm-11 f_number" style="padding: 0;">
								<input type="number" name="account" id="account" minlength="11" maxlength="11" placeholder="输入你的手机号" required/>
							</div>
						</div>
					</div>
					
					<!-- 密码输入 -->
					<div class="am-form-group">
						<div class="am-g input-box">
							<label class="am-u-sm-1" style="padding: 0;">
								<img class="menu-icon" src="__PUBLIC__/home/<USER>/picture/pwd.png" alt="">
							</label>
							<div class="am-u-sm-11 f_number" style="padding: 0;">
								<input type="password" name="password" id="password" minlength="6" maxlength="15" placeholder="输入你的密码" required/>
							</div>
						</div>
					</div>
					
					<!-- 登录按钮 -->
					<div class="am-form-group">
						<div class="">
							<button type="button" id="login-button" class="am-btn">登录</button>
						</div>
						<div style="height: 10px;"></div>
						<div style="text-align: center;">
							<span style="color: rgb(255, 209, 27);" onclick="javascript:window.location.href='{:U('User/signup')}'">没有账户？立即注册</span>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>

	<div class="message">
		<p></p>
	</div>

	<script type="text/javascript">
		document.documentElement.addEventListener('touchmove', function (event) {
			if (event.touches.length > 1) {
				event.preventDefault();
			}
		}, false);
		var sms_off = {:C('cfg_sms_off')};
	</script>

	<!--[if lt IE 9]>
<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
<script src="__PUBLIC__/home/<USER>/js/modernizr.js"></script>
<script src="__PUBLIC__/home/<USER>/js/amazeui.ie8polyfill.min.js"></script>
<![endif]-->

	<!--[if (gte IE 9)|!(IE)]><!-->
	<script src="__PUBLIC__/home/<USER>/js/jquery3.2.min.js"></script>
	<!--<![endif]-->
	<script src="__PUBLIC__/home/<USER>/js/amazeui.min.js"></script>
	<script type="text/javascript" src="__PUBLIC__/home/<USER>/js/login.js"></script>

	<div style="display: none;">
		<Somnus:sitecfg name="sitecode" />
	</div>

</body>
</html>
